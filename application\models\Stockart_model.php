<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Stockart_model extends CI_Model {
    public function get_stockart_data() {
        try {
            // Menggunakan CodeIgniter Query Builder dengan DISTINCT
            $this->db->distinct();

            // Query with user audit trail fields and order_resep status
            $select_fields = 'iri.id_pengajuan, mp.NAMA as nama_pasien, mp.NORM as norm_pasien, iri.nokun, iri.status, ior.status as order_resep_status, master.getNamaLengkapPegawai(md.NIP) as nama_dokter_tujuan, iri.created_at as dibuat_pada';
            $select_fields .= ', iri.updated_at as diubah_pada';
            $select_fields .= ', master.getNamaLengkapPegawai(ap_created.nip) as dibuat_oleh';
            $select_fields .= ', master.getNamaLengkapPegawai(ap_updated.nip) as diubah_oleh';

            $this->db->select($select_fields);
            $this->db->from('inventory.request_item iri');
            $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = iri.nokun');
            $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN');
            $this->db->join('master.pasien mp', 'mp.NORM = pp.NORM');
            $this->db->join('pendaftaran.tujuan_pasien ptp', 'ptp.NOPEN = pk.NOPEN');
            $this->db->join('master.dokter md', 'md.ID = ptp.DOKTER');
            $this->db->join('layanan.order_resep ior', 'ior.NOMOR = iri.ref', 'left'); // Join dengan order_resep menggunakan ref = NOMOR
            $this->db->join('aplikasi.pengguna ap_created', 'ap_created.id = iri.created_by', 'left');
            $this->db->join('aplikasi.pengguna ap_updated', 'ap_updated.id = iri.updated_by', 'left');
            $this->db->where('iri.status !=', 0); // Filter untuk tidak menampilkan status 0
            $this->db->where('category', 2);
            $this->db->order_by('iri.created_at', 'DESC'); // Sort by created_at descending
            $this->db->order_by('iri.id_pengajuan', 'DESC'); // Then by id_pengajuan descending

            $query = $this->db->get();

            if (!$query) {
                log_message('error', 'Query failed in Stockart_model: ' . $this->db->error()['message']);
                return array();
            }

            $result = $query->result_array();

            // Post-process to ensure all audit fields are populated
            foreach ($result as &$row) {
                // Handle diubah_pada - use created_at as fallback if empty
                if (empty($row['diubah_pada']) || $row['diubah_pada'] == '0000-00-00 00:00:00') {
                    $row['diubah_pada'] = $row['dibuat_pada'];
                }

                // Handle dibuat_oleh - use default if empty
                if (empty($row['dibuat_oleh'])) {
                    $row['dibuat_oleh'] = 'Admin Farmasi';
                }

                // Handle diubah_oleh - use dibuat_oleh as fallback if empty
                if (empty($row['diubah_oleh'])) {
                    $row['diubah_oleh'] = $row['dibuat_oleh'];
                }
            }

            log_message('info', 'Stockart query executed successfully. Rows returned: ' . count($result));

            return $result;

        } catch (Exception $e) {
            log_message('error', 'Exception in Stockart_model: ' . $e->getMessage());

            // Fallback query with basic fields only
            try {
                $this->db->distinct();
                $this->db->select('iri.id_pengajuan, mp.NAMA as nama_pasien, mp.NORM as norm_pasien, iri.nokun, iri.status, ior.status as order_resep_status, master.getNamaLengkapPegawai(md.NIP) as nama_dokter_tujuan, iri.created_at as dibuat_pada');
                $this->db->from('inventory.request_item iri');
                $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = iri.nokun');
                $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN');
                $this->db->join('master.pasien mp', 'mp.NORM = pp.NORM');
                $this->db->join('pendaftaran.tujuan_pasien ptp', 'ptp.NOPEN = pk.NOPEN');
                $this->db->join('master.dokter md', 'md.ID = ptp.DOKTER');
                $this->db->join('layanan.order_resep ior', 'ior.NOMOR = iri.ref', 'left'); // Join dengan order_resep menggunakan ref = NOMOR
                $this->db->where('iri.status !=', 0);
                $this->db->where('category', 2);
                $this->db->order_by('iri.created_at', 'DESC');
                $this->db->order_by('iri.id_pengajuan', 'DESC');

                $query = $this->db->get();
                $result = $query ? $query->result_array() : array();

                // Add missing audit fields with default values
                foreach ($result as &$row) {
                    $row['diubah_pada'] = $row['dibuat_pada'];
                    $row['dibuat_oleh'] = 'System';
                    $row['diubah_oleh'] = 'System';
                }

                return $result;

            } catch (Exception $e2) {
                log_message('error', 'Fallback query also failed: ' . $e2->getMessage());
                return array();
            }
        }
    }
    
    public function get_stockart_detail($id_pengajuan) {
        try {
            // Menggunakan CodeIgniter Query Builder
            $this->db->select('ib.NAMA as nama_barang, iri.quantity, iri.note, iri.id_item, iri.nokun, ibr.ID as id_barang_ruangan, ptp.DOKTER as id_dokter, iri.created_at, iri.updated_at, ap_created.NAMA as dibuat_oleh, ap_updated.NAMA as diupdate_oleh');
            $this->db->from('inventory.request_item iri');
            $this->db->join('inventory.barang ib', 'ib.ID = iri.id_item');
            $this->db->join('inventory.barang_ruangan ibr', 'ibr.BARANG = ib.ID');
            $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = iri.nokun');
            $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN');
            $this->db->join('master.pasien mp', 'mp.NORM = pp.NORM');
            $this->db->join('pendaftaran.tujuan_pasien ptp', 'ptp.NOPEN = pk.NOPEN');
            $this->db->join('aplikasi.pengguna ap_created', 'ap_created.ID = iri.created_by', 'left');
            $this->db->join('aplikasi.pengguna ap_updated', 'ap_updated.ID = iri.updated_by', 'left');
            $this->db->where('iri.id_pengajuan', $id_pengajuan);
            $this->db->order_by('iri.id');
            $this->db->group_by('iri.id_item, iri.created_at, iri.updated_at, ap_created.NAMA, ap_updated.NAMA');
            
            $query = $this->db->get();
            
            if (!$query) {
                log_message('error', 'Query failed in get_stockart_detail: ' . $this->db->error()['message']);
                return array();
            }
            
            $result = $query->result_array();
            return $result;
            
        } catch (Exception $e) {
            log_message('error', 'Exception in get_stockart_detail: ' . $e->getMessage());
            return array();
        }
    }
    public function generateNoOrderResep($farmasi_tujuan, $date)
    {
        $query = $this->db->query('SELECT generator.generateNoOrderResep("' . $farmasi_tujuan . '","' . $date . '") KODE')->row();
        return $query->KODE;
    }
    
    public function get_status_text($status, $order_resep_status = null) {
        // Implementasi kebijakan status baru dengan join ke order_resep
        if ($status == 1) {
            return 'Diajukan';
        } elseif ($status == 2) {
            if ($order_resep_status == 0) {
                return 'Ditolak';
            } elseif ($order_resep_status == 1) {
                return 'Diproses';
            } elseif ($order_resep_status == 2) {
                return 'Diterima';
            } else {
                // Fallback jika tidak ada order_resep atau status tidak dikenal
                return 'Diproses';
            }
        } elseif ($status == 3) {
            return 'Ditolak';
        } else {
            return 'Unknown';
        }
    }
    
    public function get_pengajuan_info($id_pengajuan) {
        try {
            // Mengambil informasi header pengajuan termasuk status dan order_resep_status
            $this->db->select('iri.created_at, iri.updated_at, iri.status, ior.status as order_resep_status, ap_created.NAMA as dibuat_oleh, ap_updated.NAMA as diupdate_oleh');
            $this->db->from('inventory.request_item iri');
            $this->db->join('layanan.order_resep ior', 'ior.NOMOR = iri.ref', 'left'); // Join dengan order_resep
            $this->db->join('aplikasi.pengguna ap_created', 'ap_created.ID = iri.created_by', 'left');
            $this->db->join('aplikasi.pengguna ap_updated', 'ap_updated.ID = iri.updated_by', 'left');
            $this->db->where('iri.id_pengajuan', $id_pengajuan);
            $this->db->limit(1);
            
            $query = $this->db->get();
            
            if (!$query) {
                log_message('error', 'Query failed in get_pengajuan_info: ' . $this->db->error()['message']);
                return array();
            }
            
            $result = $query->row_array();
            return $result ? $result : array();
            
        } catch (Exception $e) {
            log_message('error', 'Exception in get_pengajuan_info: ' . $e->getMessage());
            return array();
        }
    }
    
    public function get_user_nip($user_id) {
        try {
            $query = $this->db->query("SELECT ap.NIP FROM aplikasi.pengguna ap WHERE ap.ID = ?", array($user_id));
            return $query->row() ? $query->row()->NIP : null;
        } catch (Exception $e) {
            log_message('error', 'Exception in get_user_nip: ' . $e->getMessage());
            return null;
        }
    }
    
    public function get_nama_lengkap_pegawai($nip) {
        try {
            $query = $this->db->query("SELECT master.getNamaLengkapPegawai(?) as nama_lengkap", array($nip));
            return $query->row() ? $query->row()->nama_lengkap : null;
        } catch (Exception $e) {
            log_message('error', 'Exception in get_nama_lengkap_pegawai: ' . $e->getMessage());
            return null;
        }
    }
    
    public function insert_order_resep($data) {
        try {
            return $this->db->insert('layanan.order_resep', $data);
        } catch (Exception $e) {
            log_message('error', 'Exception in insert_order_resep: ' . $e->getMessage());
            return false;
        }
    }
    
    public function insert_order_resep_detail($data) {
        try {
            return $this->db->insert('layanan.order_detil_resep', $data);
        } catch (Exception $e) {
            log_message('error', 'Exception in insert_order_resep_detail: ' . $e->getMessage());
            return false;
        }
    }
    
    public function update_request_item_status($id_pengajuan, $status, $user_id) {
        try {
            $this->db->where('id_pengajuan', $id_pengajuan);
            return $this->db->update('inventory.request_item', array(
                'status' => $status,
                'updated_by' => $user_id,
                'updated_at' => date('Y-m-d H:i:s')
            ));
        } catch (Exception $e) {
            log_message('error', 'Exception in update_request_item_status: ' . $e->getMessage());
            return false;
        }
    }
    
    public function test_database_connection()
    {
        $test_query = $this->db->query("SELECT 'Database Connected' as status");
        return $test_query ? true : false;
    }
    
    public function process_order($id_pengajuan, $user_id) {
        try {
            // Start transaction
            $this->db->trans_start();
            
            // Get detail items untuk order
            $items = $this->get_stockart_detail($id_pengajuan);
            
            if (empty($items)) {
                return array('success' => false, 'message' => 'Tidak ada item untuk diorder');
            }
            
            // Get kunjungan dan dokter dari item pertama
            $first_item = $items[0];
            $nokun = $first_item['nokun'] ?? '';
            $id_dokter = $first_item['id_dokter'] ?? '';
            
            $tanggal = date("Y-m-d");
            $farmasi_tujuan = 105050156;
            $kode = $this->generateNoOrderResep($farmasi_tujuan, $tanggal);
            
            // Get NIP dan nama pegawai
            $user_nip = $this->get_user_nip($user_id);
            $nama_pegawai = $this->get_nama_lengkap_pegawai($user_nip);
            
            // Insert ke layanan.order_resep
            $dataOrderResep = [
                'NOMOR' => $kode,
                'KUNJUNGAN' => $nokun,
                'TANGGAL' => date("Y-m-d H:i:s"),
                'DOKTER_DPJP' => $id_dokter,
                'TUJUAN' => $farmasi_tujuan,
                'PEMBERI_RESEP' => $nama_pegawai,
                'OLEH' => $user_id,
            ];
            
            $insert_order = $this->insert_order_resep($dataOrderResep);
            
            if (!$insert_order) {
                $this->db->trans_rollback();
                return array('success' => false, 'message' => 'Gagal menyimpan order resep');
            }
            
            // Insert detail items ke layanan.order_resep_detail
            foreach ($items as $item) {
                $dataOrderResepDetail = [
                    'ORDER_ID' => $kode,
                    'FARMASI' => $item['id_item'] ?? '',
                    'STOK' => $item['id_barang_ruangan'] ?? '',
                    'JUMLAH' => $item['quantity'] ?? 0,
                    'KETERANGAN' => $item['note'] ?? '',
                ];
                
                $insert_detail = $this->insert_order_resep_detail($dataOrderResepDetail);
                
                if (!$insert_detail) {
                    $this->db->trans_rollback();
                    return array('success' => false, 'message' => 'Gagal menyimpan detail order resep');
                }
            }
            
            // Update status request_item menjadi 2 (ordered)
            $update_status = $this->update_request_item_status($id_pengajuan, 2, $user_id);
            
            if (!$update_status) {
                $this->db->trans_rollback();
                return array('success' => false, 'message' => 'Gagal mengupdate status pengajuan');
            }
            
            // Complete transaction
            $this->db->trans_complete();
            
            if ($this->db->trans_status() === FALSE) {
                return array('success' => false, 'message' => 'Gagal menyimpan order');
            } else {
                return array('success' => true, 'message' => 'Order berhasil dibuat dengan nomor: ' . $kode);
            }
            
        } catch (Exception $e) {
            $this->db->trans_rollback();
            log_message('error', 'Exception in process_order: ' . $e->getMessage());
            return array('success' => false, 'message' => $e->getMessage());
        }
    }
}