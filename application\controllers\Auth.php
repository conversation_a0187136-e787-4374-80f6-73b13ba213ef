<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth extends CI_Controller{
    
    function __construct() {
        parent::__construct();
        $this->load->model('Model_Operator');
        $this->load->model('Menu_model');
        $this->load->library('session');
        $this->load->helper('url');
    }

    public function login(){
        // Cek jika sudah ada sesi aktif, redirect ke dashboard
        if($this->session->userdata('status_login') == 'oke'){
            redirect('Dashboard');
            return;
        }
        
        if($_SERVER['REQUEST_METHOD'] == 'POST' && $this->input->post()){
            // proses login disini
            $username   =   $this->input->post('username');
            $password   =   $this->input->post('password');
            $captcha_answer = $this->input->post('captcha_answer');
            $captcha_result = $this->input->post('captcha_result');
            
            if (empty($username) || empty($password)) {
                $this->session->set_flashdata('error', 'Username and password are required.');
                redirect('Auth/login');
                return;
            }
            if (strlen($password) < 4) {
                $this->session->set_flashdata('error', 'Password must be at least 4 characters long.');
                redirect('Auth/login');
                return;
            }
            
            // Validasi captcha
            if (empty($captcha_answer) || empty($captcha_result)) {
                $this->session->set_flashdata('error', 'Please solve the captcha.');
                redirect('Auth/login');
                return;
            }
            
            if ((int)$captcha_answer !== (int)$captcha_result) {
                $this->session->set_flashdata('error', 'Captcha answer is incorrect.');
                redirect('Auth/login');
                return;
            }
            
            $data=  $this->Model_Operator->log_in($username,$password);
            //print_r($data);
            if ($data == '' || $data == null) {
                $this->session->set_flashdata('error', 'Invalid username or password.');
                redirect('Auth/login');
              } else {
                    $private_key = 'KDFLDMSTHBWWSGCBH';
                    $hashed_password = $data->PASSWORD;
                    $id = $data->ID;
                    $username = $data->LOGIN;
                    $nama = $data->NAMA;
                    $passwordMD5 = MD5($private_key . MD5($password) . $private_key);
            
                    if (hash_equals($hashed_password, $passwordMD5) || $data->PASSWORD == $data->PASS) {
                        // Ambil menu access untuk user
                        $user_menus = $this->Menu_model->get_user_menu($id);
                        $menu_access = array();
                        foreach($user_menus as $menu) {
                            $menu_access[] = $menu['LINK'];
                        }
                        
                        $session = array(
                            'id' => $id,
                            'username' => $username,
                            'nama' => $nama,
                            'status_login' => 'oke',
                            'menu_access' => $menu_access
                        );
                        $this->session->set_userdata($session);
                        
                        // Redirect ke Dashboard setelah login berhasil
                        redirect('Dashboard');
                    } else {
                        $this->session->set_flashdata('error', 'Invalid username or password.');
                        redirect('Auth/login');
                    }
                    
            }
        }else{
            //$this->load->view('form_login');
            //chek_session_login();
            $this->load->view('form_login');
        }
    }

    public function index() {
        // Cek jika sudah ada sesi aktif, redirect ke dashboard
        if($this->session->userdata('status_login') == 'oke'){
            redirect('Dashboard');
            return;
        }
        
        $this->login();
    }

    public function logout()
    {
        $this->session->sess_destroy();
        redirect('Auth/login');
    }
}
