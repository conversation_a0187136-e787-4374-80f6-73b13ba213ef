
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MonitoringPaCendana extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('Model_pulang');
        $this->load->helper('url');
        $this->load->library('session');
        
        // Cek session login
        if($this->session->userdata('status_login') != 'oke'){
            redirect('Auth/login');
        }
    }

public function index()
{
    // Load content within the dashboard template
    $data['page_content'] = 'monitoring_pa_cendana';
    $data['title'] = 'Monitoring PA Cendana';
    $this->load->view('template_dashboard', $data);
}

public function dashboard()
{
    // Load the monitoring content within the dashboard template
    $data['page_content'] = 'monitoring_pa_cendana';
    $data['title'] = 'Monitoring PA Cendana';
    $this->load->view('template_dashboard', $data);
}

public function content_only()
{
    // Load only the content without template (for AJAX or standalone use)
    $this->load->view('content_monitoring_pa_cendana');
}

public function get_monitoring_data() {
    try {
        $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d H:i:s');
        $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d H:i:s');
        $status_filter = $this->input->post('status_filter') ?? '';

        $listData = $this->Model_pulang->listHistoSito($tglAwal, $tglAkhir);

        $data = array();
        $nomor = 1;

        if ($listData && $listData->num_rows() > 0) {
            foreach ($listData->result() as $row) {
                // Determine color status based on STATUS_LIS and FNL
                $colorStatus = $this->determineColorStatus($row->STATUS_LIS ?? '', $row->FNL ?? '');
                
                // Apply status filter if specified
                if (!empty($status_filter) && $colorStatus !== $status_filter) {
                    continue;
                }

                $data[] = array(
                    $row->NOPA ?? '',
                    $row->NAMA_PASIEN ?? '',
                    $row->NORM ?? '',
                    $row->DSPA ?? '',
                    $row->RUANG_ASAL ?? '',
                    $row->MASUK_SAMPEL ?? '',
                    $row->TANGGAL_HASIL ?? '',
                    $row->LAMA_PEMERIKSAAN ?? '',
                    $row->JENIZ ?? '',
                    $row->FNL ?? '',
                    $row->STATUS_LIS ?? '',
                    $colorStatus // Add color status as last column
                );
            }
        }

        $output = array(
            "draw" => intval($this->input->post("draw")),
            "recordsTotal" => count($data),
            "recordsFiltered" => count($data),
            "data" => $data
        );

        echo json_encode($output);

    } catch (Exception $e) {
        $output = array(
            "draw" => intval($this->input->post("draw")),
            "recordsTotal" => 0,
            "recordsFiltered" => 0,
            "data" => array(),
            "error" => $e->getMessage()
        );

        echo json_encode($output);
    }
}

private function determineColorStatus($statusLis, $fnl) {
    // Convert status based on FNL (Final/Belum Final) and STATUS_LIS (Green/Yellow/Red)
    $isFinal = (strtolower($fnl) === 'final');
    
    switch(strtolower($statusLis)) {
        case 'green':
            return $isFinal ? 'hijau' : 'abu_abu';
        case 'yellow':
            return $isFinal ? 'kuning' : 'cream';
        case 'red':
            return $isFinal ? 'merah' : 'hitam';
        default:
            return 'abu_abu';
    }
}

public function get_histo_sito_data() {
    $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d H:i:s');
    $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d H:i:s');

    $listData = $this->Model_pulang->listHistoSito($tglAwal, $tglAkhir);

    $data = array();
    foreach ($listData->result() as $row) {
        $data[] = array(
            $row->NOPA,
            $row->NAMA_PASIEN,
            $row->NORM,
            $row->DSPA,
            $row->RUANG_ASAL,
            $row->MASUK_SAMPEL,
            $row->TANGGAL_HASIL,
            $row->LAMA_PEMERIKSAAN,
            $row->JENIZ,
            $row->FNL,
            $row->STATUS_LIS,
            $row->JENIS_PERIKSAAN
        );
    }

    $output = array(
        "draw" => intval($this->input->post("draw")),
        "recordsTotal" => $listData->num_rows(),
        "recordsFiltered" => $listData->num_rows(),
        "data" => $data
    );

    echo json_encode($output);
}

}
