// Debug script untuk Petugas Management
console.log('Petugas Debug script loaded');

// Function untuk test semua endpoint petugas
function testAllPetugasEndpoints() {
    console.log('=== Testing All Petugas Endpoints ===');

    // Test get_petugas_stats
    var base_url = window.base_url || (window.location.origin + '/towercmonitoring/');
    $.ajax({
        url: base_url + 'AdminMenu/get_petugas_stats',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            console.log('✓ get_petugas_stats SUCCESS:', response);
        },
        error: function(xhr, status, error) {
            console.error('✗ get_petugas_stats ERROR:', status, error);
            console.error('Response:', xhr.responseText);
        }
    });

    // Test get_petugas_data
    $.ajax({
        url: base_url + 'AdminMenu/get_petugas_data',
        type: 'POST',
        dataType: 'json',
        data: {
            draw: 1,
            start: 0,
            length: 10,
            search: { value: '', regex: false },
            order: [{ column: 0, dir: 'asc' }],
            columns: [
                { data: null, searchable: false, orderable: false },
                { data: 'no_absen', searchable: true, orderable: true },
                { data: 'nama', searchable: true, orderable: true },
                { data: 'nomor_hp', searchable: true, orderable: true },
                { data: 'status', searchable: true, orderable: false },
                { data: null, searchable: false, orderable: false }
            ]
        },
        success: function(response) {
            console.log('✓ get_petugas_data SUCCESS:', response);
        },
        error: function(xhr, status, error) {
            console.error('✗ get_petugas_data ERROR:', status, error);
            console.error('Response:', xhr.responseText);
        }
    });
}

// Function untuk monitor tab changes
function monitorTabChanges() {
    console.log('=== Monitoring Tab Changes ===');
    
    // Monitor semua event tab
    $('#adminMenuTabs button[data-bs-toggle="tab"]').on('show.bs.tab', function(e) {
        console.log('Tab show.bs.tab:', $(e.target).attr('data-bs-target'));
    });
    
    $('#adminMenuTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
        console.log('Tab shown.bs.tab:', $(e.target).attr('data-bs-target'));
        
        if ($(e.target).attr('data-bs-target') === '#petugas-pane') {
            console.log('Petugas tab is now active, checking DataTable...');
            
            setTimeout(function() {
                if ($.fn.DataTable.isDataTable('#petugasTable')) {
                    console.log('✓ Petugas DataTable exists');
                    var table = $('#petugasTable').DataTable();
                    console.log('DataTable info:', table.page.info());
                } else {
                    console.log('✗ Petugas DataTable does not exist');
                }
            }, 100);
        }
    });
    
    $('#adminMenuTabs button[data-bs-toggle="tab"]').on('hide.bs.tab', function(e) {
        console.log('Tab hide.bs.tab:', $(e.target).attr('data-bs-target'));
    });
    
    $('#adminMenuTabs button[data-bs-toggle="tab"]').on('hidden.bs.tab', function(e) {
        console.log('Tab hidden.bs.tab:', $(e.target).attr('data-bs-target'));
    });
}

// Function untuk check DOM elements
function checkDOMElements() {
    console.log('=== Checking DOM Elements ===');
    
    console.log('Petugas tab exists:', $('#petugas-tab').length > 0);
    console.log('Petugas pane exists:', $('#petugas-pane').length > 0);
    console.log('Petugas table exists:', $('#petugasTable').length > 0);
    console.log('Admin menu tabs exists:', $('#adminMenuTabs').length > 0);
    
    console.log('Petugas tab classes:', $('#petugas-tab').attr('class'));
    console.log('Petugas pane classes:', $('#petugas-pane').attr('class'));
    
    // Check if DataTable is initialized
    if ($.fn.DataTable.isDataTable('#petugasTable')) {
        console.log('✓ Petugas DataTable is initialized');
    } else {
        console.log('✗ Petugas DataTable is NOT initialized');
    }
}

// Function untuk force initialize petugas tab
function forceInitPetugasTab() {
    console.log('=== Force Initialize Petugas Tab ===');
    
    // Activate petugas tab
    $('#petugas-tab').tab('show');
    
    setTimeout(function() {
        if (typeof initPetugasTab === 'function') {
            console.log('Calling initPetugasTab()...');
            initPetugasTab();
        } else {
            console.error('initPetugasTab function not found!');
        }
    }, 300);
}

// Auto-run debug functions when script loads
$(document).ready(function() {
    setTimeout(function() {
        console.log('\n=== PETUGAS DEBUG STARTED ===');
        checkDOMElements();
        monitorTabChanges();
        
        // Test endpoints after a delay
        setTimeout(function() {
            testAllPetugasEndpoints();
        }, 1000);
        
    }, 500);
});

// Expose functions to global scope for manual testing
window.testAllPetugasEndpoints = testAllPetugasEndpoints;
window.monitorTabChanges = monitorTabChanges;
window.checkDOMElements = checkDOMElements;
window.forceInitPetugasTab = forceInitPetugasTab;